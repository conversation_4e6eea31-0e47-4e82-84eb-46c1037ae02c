import React, { useState, useMemo } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { ChevronDown, ChevronUp, Package, Clipboard as ClipboardIcon, RefreshCw, AlertCircle, CheckCircle } from 'lucide-react-native';
import * as Clipboard from 'expo-clipboard';
import Colors from '@/constants/colors';
import { Formulation } from '@/types/formulation';
import { useInventoryStore } from '@/stores/inventory-store';
import { parseFormulaTextToProducts } from '@/utils/parseFormula';
import { InventoryConsumptionService } from '@/services/inventoryConsumptionService';

interface MaterialsSummaryCardProps {
  formulationData: Formulation | null;
  formulaText: string;
  selectedBrand: string;
  selectedLine: string;
}

interface MaterialItem {
  productName: string;
  totalQuantity: number;
  unit: string;
  inStock?: boolean;
  matchedProduct?: {
    id: string;
    name: string;
    displayName?: string;
    confidence: number;
  };
  matchType?: 'exact' | 'partial' | 'fuzzy' | 'none';
}

export const MaterialsSummaryCard: React.FC<MaterialsSummaryCardProps> = ({
  formulationData,
  formulaText,
  selectedBrand,
  selectedLine
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const { products } = useInventoryStore();

  // Extract materials from structured data or parse from text
  const materials = useMemo<MaterialItem[]>(() => {
    const materialMap = new Map<string, MaterialItem>();
    
    // Return empty array if no formula text
    if (!formulaText) return [];

    if (formulationData && formulationData.steps) {
      // Use structured data
      formulationData.steps.forEach(step => {
        if (step.mix) {
          step.mix.forEach(product => {
            const key = product.productName;
            const existing = materialMap.get(key);
            
            if (existing) {
              existing.totalQuantity += product.quantity;
            } else {
              materialMap.set(key, {
                productName: product.productName,
                totalQuantity: product.quantity,
                unit: product.unit
              });
            }
          });
        }
      });
    } else if (formulaText) {
      // Use the unified parser
      const products = parseFormulaTextToProducts(formulaText);
      
      products.forEach(product => {
        const key = product.name;
        const existing = materialMap.get(key);
        
        if (existing) {
          existing.totalQuantity += product.amount;
        } else {
          materialMap.set(key, {
            productName: product.name,
            totalQuantity: product.amount,
            unit: product.unit
          });
        }
      });
    }

    // Check stock availability and match products
    const materialsArray = Array.from(materialMap.values());
    materialsArray.forEach(material => {
      // Try structured matching first if we have the data
      let matches: any[] = [];
      
      if (formulationData && formulationData.steps) {
        // Extract structured data from the material
        const structuredData = formulationData.steps
          .flatMap(step => step.mix || [])
          .find(p => p.productName === material.productName);
          
        if (structuredData) {
          matches = InventoryConsumptionService.findMatchingProductsStructured({
            brand: structuredData.brand,
            line: structuredData.line,
            type: structuredData.type,
            shade: structuredData.shade,
            name: material.productName
          });
        }
      }
      
      // Fallback to name-based matching
      if (matches.length === 0) {
        matches = InventoryConsumptionService.findMatchingProducts(material.productName);
      }
      
      const bestMatch = matches[0];
      
      if (bestMatch && bestMatch.matchScore > 60) {
        const product = bestMatch.product;
        material.inStock = product.currentStock >= material.totalQuantity;
        material.matchedProduct = {
          id: product.id,
          name: product.name,
          displayName: product.displayName,
          confidence: bestMatch.confidence || bestMatch.matchScore
        };
        material.matchType = bestMatch.matchType;
      } else {
        material.inStock = false;
        material.matchType = 'none';
      }
    });

    return materialsArray;
  }, [formulationData, formulaText, selectedBrand, selectedLine, products]);

  const copyToClipboard = async () => {
    const text = materials.map(m => 
      `• ${m.productName}: ${m.totalQuantity}${m.unit}`
    ).join('\n');

    const fullText = `Lista de Materiales - ${selectedBrand} ${selectedLine}\n\n${text}`;
    
    await Clipboard.setStringAsync(fullText);
    Alert.alert('Copiado', 'Lista de materiales copiada al portapapeles');
  };

  const totalItems = materials.length;
  const itemsInStock = materials.filter(m => m.inStock).length;
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefreshStock = async () => {
    setIsRefreshing(true);
    // Force re-render by updating a dummy state
    // The useMemo will re-calculate stock availability
    await new Promise(resolve => setTimeout(resolve, 500));
    setIsRefreshing(false);
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity 
        style={styles.header}
        onPress={() => setIsExpanded(!isExpanded)}
        activeOpacity={0.7}
      >
        <View style={styles.headerLeft}>
          <Package size={20} color={Colors.light.primary} />
          <View style={styles.headerText}>
            <Text style={styles.title}>Lista de Compra</Text>
            <Text style={styles.subtitle}>
              {totalItems} productos • {itemsInStock}/{totalItems} en stock
            </Text>
          </View>
        </View>
        <View style={styles.headerRight}>
          <TouchableOpacity
            onPress={handleRefreshStock}
            disabled={isRefreshing}
            style={styles.refreshButton}
          >
            <RefreshCw 
              size={18} 
              color={isRefreshing ? Colors.light.gray : Colors.light.primary} 
              style={isRefreshing ? styles.rotating : undefined}
            />
          </TouchableOpacity>
          {isExpanded ? (
            <ChevronUp size={20} color={Colors.light.gray} />
          ) : (
            <ChevronDown size={20} color={Colors.light.gray} />
          )}
        </View>
      </TouchableOpacity>

      {isExpanded && (
        <View style={styles.content}>
          {materials.map((material, index) => (
            <View key={index} style={styles.materialItem}>
              <View style={styles.materialInfo}>
                <Text style={styles.materialName}>{material.productName}</Text>
                {material.matchedProduct && (
                  <View style={styles.matchInfo}>
                    <Text style={styles.matchedProductName}>
                      → {material.matchedProduct.displayName || material.matchedProduct.name}
                    </Text>
                    <View style={styles.confidenceBadge}>
                      {material.matchType === 'exact' ? (
                        <CheckCircle size={12} color={Colors.light.success} />
                      ) : material.matchType === 'partial' ? (
                        <AlertCircle size={12} color={Colors.light.warning} />
                      ) : (
                        <AlertCircle size={12} color={Colors.light.error} />
                      )}
                      <Text style={[
                        styles.confidenceText,
                        material.matchType === 'exact' ? styles.highConfidence :
                        material.matchType === 'partial' ? styles.mediumConfidence : styles.lowConfidence
                      ]}>
                        {material.matchedProduct.confidence}%
                      </Text>
                    </View>
                  </View>
                )}
                <Text style={styles.materialQuantity}>
                  {material.totalQuantity}{material.unit}
                </Text>
              </View>
              <View style={[
                styles.stockIndicator,
                material.inStock ? styles.inStock : styles.outOfStock
              ]}>
                <Text style={[
                  styles.stockText,
                  material.inStock ? styles.stockTextInStock : styles.stockTextOutOfStock
                ]}>
                  {material.matchType === 'none' ? 'No encontrado' : 
                   material.inStock ? 'En stock' : 'Falta'}
                </Text>
              </View>
            </View>
          ))}

          <TouchableOpacity style={styles.copyButton} onPress={copyToClipboard}>
            <ClipboardIcon size={16} color={Colors.light.primary} />
            <Text style={styles.copyButtonText}>Copiar lista</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.surface,
    borderRadius: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerText: {
    marginLeft: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  subtitle: {
    fontSize: 13,
    color: Colors.light.gray,
    marginTop: 2,
  },
  content: {
    padding: 16,
    paddingTop: 0,
  },
  materialItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  materialInfo: {
    flex: 1,
  },
  materialName: {
    fontSize: 14,
    color: Colors.light.text,
    marginBottom: 2,
  },
  materialQuantity: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  stockIndicator: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 12,
  },
  inStock: {
    backgroundColor: Colors.light.success + '15',
  },
  outOfStock: {
    backgroundColor: Colors.light.error + '15',
  },
  stockText: {
    fontSize: 12,
    fontWeight: '500',
  },
  stockTextInStock: {
    color: Colors.light.success,
  },
  stockTextOutOfStock: {
    color: Colors.light.error,
  },
  copyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
    paddingVertical: 12,
    paddingHorizontal: 20,
    backgroundColor: Colors.light.primary + '10',
    borderRadius: 12,
    gap: 8,
  },
  copyButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.primary,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  refreshButton: {
    padding: 4,
  },
  rotating: {
    transform: [{ rotate: '360deg' }],
  },
  matchInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    gap: 8,
  },
  matchedProductName: {
    fontSize: 12,
    color: Colors.light.gray,
    flex: 1,
    fontStyle: 'italic',
  },
  confidenceBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
    backgroundColor: Colors.light.border,
  },
  confidenceText: {
    fontSize: 11,
    fontWeight: '600',
  },
  highConfidence: {
    color: Colors.light.success,
  },
  mediumConfidence: {
    color: Colors.light.warning,
  },
  lowConfidence: {
    color: Colors.light.error,
  },
});