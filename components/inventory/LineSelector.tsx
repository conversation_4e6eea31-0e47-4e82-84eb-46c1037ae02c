import React, { useState, useMemo } from 'react';
import { View, Text, TouchableOpacity, FlatList, TextInput, StyleSheet } from 'react-native';
import { Brand, ProductLine, getLinesByBrandId } from '@/constants/reference-data/brands-data';
import { Ionicons } from '@expo/vector-icons';

interface LineSelectorProps {
  selectedBrand: Brand | null;
  selectedLine: ProductLine | null;
  onLineSelect: (line: ProductLine | null) => void;
  placeholder?: string;
  disabled?: boolean;
}

export function LineSelector({ selectedBrand, selectedLine, onLineSelect, placeholder = "Seleccionar línea", disabled = false }: LineSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const availableLines = useMemo(() => {
    if (!selectedBrand) return [];
    return getLinesByBrandId(selectedBrand.id);
  }, [selectedBrand]);

  const filteredLines = useMemo(() => {
    if (!searchQuery.trim()) {
      return availableLines;
    }
    return availableLines.filter(line => 
      line.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (line.description && line.description.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  }, [availableLines, searchQuery]);

  const handleLineSelect = (line: ProductLine) => {
    onLineSelect(line);
    setIsOpen(false);
    setSearchQuery('');
  };

  const handleClear = () => {
    onLineSelect(null);
    setSearchQuery('');
  };

  const renderLineItem = ({ item }: { item: ProductLine }) => (
    <TouchableOpacity
      style={styles.lineItem}
      onPress={() => handleLineSelect(item)}
    >
      <View style={styles.lineInfo}>
        <Text style={styles.lineName}>{item.name}</Text>
        {item.description && (
          <Text style={styles.lineDescription}>{item.description}</Text>
        )}
      </View>
    </TouchableOpacity>
  );

  if (!selectedBrand) {
    return (
      <View style={[styles.selector, styles.selectorDisabled]}>
        <View style={styles.selectorContent}>
          <Text style={styles.placeholderText}>Primero selecciona una marca</Text>
          <Ionicons name="chevron-down" size={20} color="#ccc" />
        </View>
      </View>
    );
  }

  if (isOpen) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Líneas de {selectedBrand.name}</Text>
          <TouchableOpacity onPress={() => setIsOpen(false)}>
            <Ionicons name="close" size={24} color="#666" />
          </TouchableOpacity>
        </View>
        
        {availableLines.length > 5 && (
          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Buscar línea..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoFocus
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <Ionicons name="close-circle" size={20} color="#666" />
              </TouchableOpacity>
            )}
          </View>
        )}

        {filteredLines.length > 0 ? (
          <FlatList
            data={filteredLines}
            renderItem={renderLineItem}
            keyExtractor={(item) => item.id}
            style={styles.lineList}
            showsVerticalScrollIndicator={false}
            scrollEnabled={false}
          />
        ) : (
          <View style={styles.emptyState}>
            <Text style={styles.emptyText}>
              {searchQuery ? 'No se encontraron líneas que coincidan' : 'Esta marca no tiene líneas configuradas'}
            </Text>
          </View>
        )}

        {selectedLine && (
          <TouchableOpacity style={styles.clearButton} onPress={handleClear}>
            <Text style={styles.clearButtonText}>Limpiar selección</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }

  return (
    <TouchableOpacity
      style={[styles.selector, disabled && styles.selectorDisabled]}
      onPress={() => !disabled && setIsOpen(true)}
      disabled={disabled}
    >
      <View style={styles.selectorContent}>
        <Text style={[styles.selectorText, !selectedLine && styles.placeholderText]}>
          {selectedLine ? selectedLine.name : placeholder}
        </Text>
        <Ionicons name="chevron-down" size={20} color="#666" />
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 16,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333',
  },
  lineList: {
    maxHeight: 300,
  },
  lineItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  lineInfo: {
    gap: 4,
  },
  lineName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  lineDescription: {
    fontSize: 14,
    color: '#666',
  },
  emptyState: {
    paddingVertical: 32,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
  },
  clearButton: {
    marginTop: 16,
    paddingVertical: 12,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    alignItems: 'center',
  },
  clearButtonText: {
    fontSize: 16,
    color: '#666',
  },
  selector: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  selectorDisabled: {
    backgroundColor: '#f0f0f0',
    opacity: 0.6,
  },
  selectorContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  selectorText: {
    fontSize: 16,
    color: '#333',
  },
  placeholderText: {
    color: '#999',
  },
});